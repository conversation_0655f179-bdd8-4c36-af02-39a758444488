// Language Management for Tab3a Printing Website

// Language configuration
const LANGUAGES = {
    ar: {
        name: 'العربية',
        dir: 'rtl',
        fontFamily: 'Cairo'
    },
    en: {
        name: 'English',
        dir: 'ltr',
        fontFamily: 'Poppins'
    },
    tr: {
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        dir: 'ltr',
        fontFamily: 'Poppins'
    }
};

// Initialize language system
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguageSystem();
});

function initializeLanguageSystem() {
    // Set up language detection
    detectUserLanguage();
    
    // Set up language switcher events
    setupLanguageSwitcher();
    
    // Apply initial language
    const currentLang = getCurrentLanguage() || 'ar';
    applyLanguageSettings(currentLang);
}

// Detect user's preferred language
function detectUserLanguage() {
    // Check if language is already saved
    const savedLanguage = localStorage.getItem('preferred_language');
    if (savedLanguage && LANGUAGES[savedLanguage]) {
        return savedLanguage;
    }
    
    // Check browser language
    const browserLang = navigator.language || navigator.userLanguage;
    const langCode = browserLang.split('-')[0];
    
    if (LANGUAGES[langCode]) {
        localStorage.setItem('preferred_language', langCode);
        return langCode;
    }
    
    // Default to Arabic
    localStorage.setItem('preferred_language', 'ar');
    return 'ar';
}

// Setup language switcher events
function setupLanguageSwitcher() {
    // Add click events to language dropdown items
    document.addEventListener('click', function(e) {
        if (e.target.closest('[onclick*="changeLanguage"]')) {
            e.preventDefault();
            const onclickAttr = e.target.getAttribute('onclick');
            const langMatch = onclickAttr.match(/changeLanguage\('(\w+)'\)/);
            if (langMatch) {
                changeLanguage(langMatch[1]);
            }
        }
    });
}

// Apply language settings
function applyLanguageSettings(language) {
    if (!LANGUAGES[language]) {
        console.error('Unsupported language:', language);
        return;
    }
    
    const langConfig = LANGUAGES[language];
    
    // Update HTML attributes
    document.documentElement.lang = language;
    document.documentElement.dir = langConfig.dir;
    
    // Update font family
    updateFontFamily(langConfig.fontFamily);
    
    // Update Bootstrap RTL/LTR classes
    updateBootstrapDirection(langConfig.dir);
    
    // Update navigation alignment
    updateNavigationAlignment(langConfig.dir);
    
    // Update form alignment
    updateFormAlignment(langConfig.dir);
    
    // Update text alignment for specific elements
    updateTextAlignment(langConfig.dir);
}

// Update font family based on language
function updateFontFamily(fontFamily) {
    const style = document.getElementById('dynamic-font-style') || document.createElement('style');
    style.id = 'dynamic-font-style';
    
    style.textContent = `
        body, .navbar, .btn, .form-control, .card, .modal {
            font-family: '${fontFamily}', sans-serif !important;
        }
    `;
    
    if (!document.getElementById('dynamic-font-style')) {
        document.head.appendChild(style);
    }
}

// Update Bootstrap direction classes
function updateBootstrapDirection(direction) {
    const body = document.body;
    
    if (direction === 'rtl') {
        body.classList.add('rtl');
        body.classList.remove('ltr');
    } else {
        body.classList.add('ltr');
        body.classList.remove('rtl');
    }
}

// Update navigation alignment
function updateNavigationAlignment(direction) {
    const navbar = document.querySelector('.navbar-nav');
    if (navbar) {
        if (direction === 'rtl') {
            navbar.classList.remove('ms-auto');
            navbar.classList.add('me-auto');
        } else {
            navbar.classList.remove('me-auto');
            navbar.classList.add('ms-auto');
        }
    }
    
    // Update navbar brand and controls alignment
    const navbarControls = document.querySelector('.navbar-nav:last-child');
    if (navbarControls) {
        if (direction === 'rtl') {
            navbarControls.classList.remove('ms-auto');
        } else {
            navbarControls.classList.add('ms-auto');
        }
    }
}

// Update form alignment
function updateFormAlignment(direction) {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (direction === 'rtl') {
                input.style.textAlign = 'right';
            } else {
                input.style.textAlign = 'left';
            }
        });
    });
}

// Update text alignment for specific elements
function updateTextAlignment(direction) {
    // Update modal footers
    const modalFooters = document.querySelectorAll('.modal-footer');
    modalFooters.forEach(footer => {
        if (direction === 'rtl') {
            footer.style.justifyContent = 'flex-start';
        } else {
            footer.style.justifyContent = 'flex-end';
        }
    });
    
    // Update card text alignment
    const cards = document.querySelectorAll('.card-body');
    cards.forEach(card => {
        if (direction === 'rtl') {
            card.style.textAlign = 'right';
        } else {
            card.style.textAlign = 'left';
        }
    });
}

// Format numbers based on language
function formatNumber(number, language = getCurrentLanguage()) {
    const locale = {
        'ar': 'ar-SA',
        'en': 'en-US',
        'tr': 'tr-TR'
    }[language] || 'ar-SA';
    
    return new Intl.NumberFormat(locale).format(number);
}

// Format currency based on language
function formatCurrency(amount, language = getCurrentLanguage()) {
    const currency = {
        'ar': 'SAR',
        'en': 'SAR',
        'tr': 'SAR'
    }[language] || 'SAR';
    
    const locale = {
        'ar': 'ar-SA',
        'en': 'en-US',
        'tr': 'tr-TR'
    }[language] || 'ar-SA';
    
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency
    }).format(amount);
}

// Format date based on language
function formatDate(date, language = getCurrentLanguage()) {
    const locale = {
        'ar': 'ar-SA',
        'en': 'en-US',
        'tr': 'tr-TR'
    }[language] || 'ar-SA';
    
    return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

// Get text direction for current language
function getTextDirection(language = getCurrentLanguage()) {
    return LANGUAGES[language]?.dir || 'rtl';
}

// Check if current language is RTL
function isRTL(language = getCurrentLanguage()) {
    return getTextDirection(language) === 'rtl';
}

// Get language name
function getLanguageName(language = getCurrentLanguage()) {
    return LANGUAGES[language]?.name || 'العربية';
}

// Validate language code
function isValidLanguage(language) {
    return LANGUAGES.hasOwnProperty(language);
}

// Get available languages
function getAvailableLanguages() {
    return Object.keys(LANGUAGES);
}

// Enhanced change language function
function changeLanguageEnhanced(language) {
    if (!isValidLanguage(language)) {
        console.error('Invalid language code:', language);
        return;
    }
    
    // Apply language settings
    applyLanguageSettings(language);
    
    // Call the main change language function
    if (typeof changeLanguage === 'function') {
        changeLanguage(language);
    }
    
    // Trigger custom event
    const event = new CustomEvent('languageChanged', {
        detail: { language, direction: getTextDirection(language) }
    });
    document.dispatchEvent(event);
}

// Listen for language change events
document.addEventListener('languageChanged', function(e) {
    console.log('Language changed to:', e.detail.language, 'Direction:', e.detail.direction);
    
    // Update any language-dependent components
    updateLanguageDependentComponents(e.detail.language);
});

// Update components that depend on language
function updateLanguageDependentComponents(language) {
    // Update date displays
    const dateElements = document.querySelectorAll('[data-date]');
    dateElements.forEach(element => {
        const dateValue = element.getAttribute('data-date');
        if (dateValue) {
            element.textContent = formatDate(dateValue, language);
        }
    });
    
    // Update number displays
    const numberElements = document.querySelectorAll('[data-number]');
    numberElements.forEach(element => {
        const numberValue = element.getAttribute('data-number');
        if (numberValue) {
            element.textContent = formatNumber(parseFloat(numberValue), language);
        }
    });
    
    // Update currency displays
    const currencyElements = document.querySelectorAll('[data-currency]');
    currencyElements.forEach(element => {
        const currencyValue = element.getAttribute('data-currency');
        if (currencyValue) {
            element.textContent = formatCurrency(parseFloat(currencyValue), language);
        }
    });
}

// Export functions for global use
window.formatNumber = formatNumber;
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.getTextDirection = getTextDirection;
window.isRTL = isRTL;
window.getLanguageName = getLanguageName;
window.isValidLanguage = isValidLanguage;
window.getAvailableLanguages = getAvailableLanguages;
window.changeLanguageEnhanced = changeLanguageEnhanced;

console.log('Language.js loaded successfully');
