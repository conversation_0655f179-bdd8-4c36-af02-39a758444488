<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang="contact_title">تواصل معنا - مطبعة طبعة</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/themes.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.html">
                <i class="fas fa-print me-2"></i>
                <span data-lang="brand_name">طبعة</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html" data-lang="nav_home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html" data-lang="nav_about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.html" data-lang="nav_services">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html" data-lang="nav_contact">تواصل معنا</a>
                    </li>
                </ul>
                
                <div class="navbar-nav">
                    <!-- Cart -->
                    <div class="nav-item me-3">
                        <button class="btn btn-outline-primary position-relative" id="cartBtn">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cartCount">0</span>
                        </button>
                    </div>
                    
                    <!-- Language Selector -->
                    <div class="nav-item dropdown me-3">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe"></i>
                            <span id="currentLang">العربية</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('ar')">العربية</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('en')">English</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('tr')">Türkçe</a></li>
                        </ul>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <div class="nav-item">
                        <button class="btn btn-outline-secondary" id="themeToggle">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="py-5 mt-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3" data-lang="contact_title">تواصل معنا</h1>
                    <p class="lead" data-lang="contact_subtitle">نحن هنا لخدمتك، تواصل معنا في أي وقت</p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-envelope display-1"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Content -->
    <section class="py-5">
        <div class="container">
            <div class="row g-5">
                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="contact-form">
                        <h3 class="mb-4" data-lang="contact_form_title">أرسل لنا رسالة</h3>
                        <form id="contactForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label" data-lang="contact_name">الاسم</label>
                                    <input type="text" class="form-control" id="name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label" data-lang="contact_email">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                                <div class="col-12">
                                    <label for="serviceType" class="form-label" data-lang="contact_service_type">نوع الخدمة</label>
                                    <select class="form-select" id="serviceType" required>
                                        <option value="" data-lang="select_service">اختر نوع الخدمة</option>
                                        <option value="business_cards" data-lang="service_business_cards">كروت شخصية</option>
                                        <option value="banners" data-lang="service_banners_rollup">لوحات ورول أب</option>
                                        <option value="invoices" data-lang="service_invoices">فواتير ودفاتر</option>
                                        <option value="bags" data-lang="service_bags_printing">طباعة أكياس</option>
                                        <option value="tshirts" data-lang="service_tshirts">طباعة تيشيرتات</option>
                                        <option value="other" data-lang="service_other">خدمات أخرى</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label" data-lang="contact_message">الرسالة</label>
                                    <textarea class="form-control" id="message" rows="5" required></textarea>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        <span data-lang="contact_send">إرسال الرسالة</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="contact-info">
                        <h3 class="mb-4" data-lang="contact_info_title">معلومات التواصل</h3>
                        
                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-phone text-primary"></i>
                                </div>
                                <div>
                                    <h6 data-lang="contact_phone">الهاتف</h6>
                                    <p class="mb-0">+966 50 123 4567</p>
                                </div>
                            </div>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-envelope text-primary"></i>
                                </div>
                                <div>
                                    <h6 data-lang="contact_email_label">البريد الإلكتروني</h6>
                                    <p class="mb-0"><EMAIL></p>
                                </div>
                            </div>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-map-marker-alt text-primary"></i>
                                </div>
                                <div>
                                    <h6 data-lang="contact_address">العنوان</h6>
                                    <p class="mb-0">الرياض، المملكة العربية السعودية</p>
                                </div>
                            </div>
                        </div>

                        <div class="contact-item mb-4">
                            <div class="d-flex align-items-center">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-clock text-primary"></i>
                                </div>
                                <div>
                                    <h6 data-lang="contact_hours">ساعات العمل</h6>
                                    <p class="mb-0" data-lang="contact_hours_value">السبت - الخميس: 9:00 ص - 6:00 م</p>
                                </div>
                            </div>
                        </div>

                        <!-- Social Links -->
                        <div class="social-links mt-4">
                            <h6 class="mb-3">تابعنا على</h6>
                            <div class="d-flex gap-3">
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-facebook"></i>
                                </a>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="btn btn-outline-primary btn-sm">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="btn btn-outline-success btn-sm">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <h3 class="text-center mb-4">موقعنا على الخريطة</h3>
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="map-container" style="height: 400px; border-radius: 8px; overflow: hidden;">
                        <iframe 
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3624.1!2d46.6753!3d24.7136!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjTCsDQyJzQ5LjAiTiA0NsKwNDAnMzEuMSJF!5e0!3m2!1sen!2ssa!4v1234567890"
                            width="100%" 
                            height="100%" 
                            style="border:0;" 
                            allowfullscreen="" 
                            loading="lazy" 
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cart Modal -->
    <div class="modal fade" id="cartModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-lang="cart_title">سلة التسوق</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="cartItems">
                        <!-- Cart items will be displayed here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">إغلاق</button>
                    <button type="button" class="btn btn-success" id="orderWhatsApp" data-lang="order_whatsapp">
                        <i class="fab fa-whatsapp me-2"></i>إتمام الطلب عبر واتساب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 data-lang="brand_name">طبعة</h5>
                    <p data-lang="footer_desc">مطبعة احترافية تقدم أفضل خدمات الطباعة</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-3">
            <div class="text-center">
                <p class="mb-0" data-lang="copyright">&copy; 2024 طبعة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../js/main.js"></script>
    <script src="../js/language.js"></script>
    <script src="../js/theme.js"></script>
    <script src="../js/cart.js"></script>
    
    <script>
        // Contact form handling
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                serviceType: document.getElementById('serviceType').value,
                message: document.getElementById('message').value
            };
            
            // Simulate form submission
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            submitBtn.innerHTML = '<span class="loading"></span>';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                // Show success message
                showNotification(getTranslation('form_success'), 'success');
                
                // Reset form
                e.target.reset();
                
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
        
        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                    <div>${message}</div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>
