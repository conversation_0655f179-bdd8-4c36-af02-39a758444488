/* Light Theme (Default) */
:root {
    --bg-color: #ffffff;
    --text-color: #1e293b;
    --card-bg: #ffffff;
    --border-color: #e2e8f0;
    --navbar-bg: rgba(248, 250, 252, 0.95);
    --modal-bg: #ffffff;
    --input-bg: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-color: #0f172a;
    --text-color: #f1f5f9;
    --card-bg: #1e293b;
    --border-color: #334155;
    --navbar-bg: rgba(15, 23, 42, 0.95);
    --modal-bg: #1e293b;
    --input-bg: #334155;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Apply theme variables */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Navigation Dark Theme */
[data-theme="dark"] .navbar {
    background-color: var(--navbar-bg) !important;
}

[data-theme="dark"] .navbar-light .navbar-nav .nav-link {
    color: var(--text-color) !important;
}

[data-theme="dark"] .navbar-light .navbar-brand {
    color: #3b82f6 !important;
}

[data-theme="dark"] .navbar-light .navbar-toggler {
    border-color: var(--border-color);
}

[data-theme="dark"] .navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28241, 245, 249, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Cards Dark Theme */
[data-theme="dark"] .service-card,
[data-theme="dark"] .product-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .card-body {
    color: var(--text-color);
}

/* Background sections */
[data-theme="dark"] .bg-light {
    background-color: #1e293b !important;
}

[data-theme="dark"] .text-muted {
    color: #94a3b8 !important;
}

/* Modal Dark Theme */
[data-theme="dark"] .modal-content {
    background-color: var(--modal-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modal-footer {
    border-top-color: var(--border-color);
}

[data-theme="dark"] .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Form Elements Dark Theme */
[data-theme="dark"] .form-control {
    background-color: var(--input-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus {
    background-color: var(--input-bg);
    border-color: #3b82f6;
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

[data-theme="dark"] .form-control::placeholder {
    color: #94a3b8;
}

[data-theme="dark"] .form-select {
    background-color: var(--input-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .form-label {
    color: var(--text-color);
}

/* Dropdown Dark Theme */
[data-theme="dark"] .dropdown-menu {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-color);
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
    background-color: #334155;
    color: var(--text-color);
}

/* Button Dark Theme Adjustments */
[data-theme="dark"] .btn-outline-secondary {
    color: var(--text-color);
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background-color: #334155;
    border-color: #334155;
    color: var(--text-color);
}

[data-theme="dark"] .btn-secondary {
    background-color: #475569;
    border-color: #475569;
}

[data-theme="dark"] .btn-secondary:hover {
    background-color: #334155;
    border-color: #334155;
}

/* Cart Item Dark Theme */
[data-theme="dark"] .cart-item {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .quantity-btn {
    background-color: var(--input-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .quantity-input {
    background-color: var(--input-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Product Image Dark Theme */
[data-theme="dark"] .product-image,
[data-theme="dark"] .cart-item-image {
    background: linear-gradient(45deg, #1e293b, #334155);
}

/* Hero Section Dark Theme */
[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

/* Footer Dark Theme */
[data-theme="dark"] footer {
    background-color: #020617 !important;
}

/* Table Dark Theme */
[data-theme="dark"] .table {
    color: var(--text-color);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > td,
[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Alert Dark Theme */
[data-theme="dark"] .alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
}

[data-theme="dark"] .alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.2);
    color: #86efac;
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #fcd34d;
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
}

/* Scrollbar Dark Theme */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: #1e293b;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* Theme Toggle Button */
.theme-toggle {
    position: relative;
    overflow: hidden;
}

.theme-toggle i {
    transition: transform 0.3s ease;
}

[data-theme="dark"] .theme-toggle .fa-moon {
    transform: rotate(180deg);
}

[data-theme="dark"] .theme-toggle .fa-sun {
    transform: rotate(0deg);
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    [data-theme="dark"] {
        --bg-color: #000000;
        --text-color: #ffffff;
        --card-bg: #1a1a1a;
        --border-color: #666666;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}
