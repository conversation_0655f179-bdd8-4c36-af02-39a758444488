{"name": "tab3a-printing-website", "version": "1.0.0", "description": "Professional printing services website with multi-language support, dark/light mode, and WhatsApp integration", "main": "index.html", "scripts": {"start": "npx serve . -p 8000", "dev": "npx serve . -p 8000 --live", "build": "echo 'No build process needed for static site'", "test": "echo 'No tests specified'", "lint": "echo 'Linting HTML, CSS, and JS files...'", "validate": "echo 'Validating HTML and checking accessibility...'", "deploy": "echo 'Deploy to your hosting provider'", "clean": "echo 'Cleaning cache and temporary files...'", "backup": "echo 'Creating backup of the project...'"}, "keywords": ["printing", "website", "multilingual", "arabic", "english", "turkish", "rtl", "dark-mode", "responsive", "bootstrap", "whatsapp", "shopping-cart", "pwa", "service-worker"], "author": {"name": "Tab3a Printing Team", "email": "<EMAIL>", "url": "https://tab3a.com"}, "license": "MIT", "homepage": "https://tab3a.com", "repository": {"type": "git", "url": "https://github.com/tab3a/printing-website.git"}, "bugs": {"url": "https://github.com/tab3a/printing-website/issues", "email": "<EMAIL>"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"serve": "^14.0.0"}, "dependencies": {}, "config": {"port": 8000, "host": "localhost"}, "directories": {"doc": "./docs", "test": "./tests"}, "files": ["index.html", "pages/", "css/", "js/", "lang/", "data/", "images/", "manifest.webmanifest", "sw.js", ".htaccess", "README.md"], "funding": {"type": "individual", "url": "https://tab3a.com/support"}, "private": false, "workspaces": [], "peerDependencies": {}, "optionalDependencies": {}, "bundleDependencies": [], "os": ["darwin", "linux", "win32"], "cpu": ["x64", "arm64"]}