# مطبعة طبعة - Tab3a Printing Website

موقع مطبعة احترافي متعدد اللغات مع دعم الوضع الليلي والنهاري وسلة التسوق المتكاملة مع واتساب.

## 🌟 الميزات الرئيسية

### 🌐 تعدد اللغات
- **العربية** (افتراضية، RTL)
- **الإنجليزية** (LTR)
- **التركية** (LTR)
- تغيير الاتجاه تلقائياً حسب اللغة
- حفظ تفضيل اللغة في المتصفح

### 🌙 الوضع الليلي والنهاري
- تبديل سلس بين الوضعين
- حفظ التفضيل في localStorage
- دعم تفضيلات النظام
- انتقالات سلسة ومتحركة

### 🛒 سلة التسوق
- إضافة وحذف المنتجات
- تعديل الكميات
- حفظ السلة في المتصفح
- إرسال الطلب مباشرة عبر واتساب

### 📱 تصميم متجاوب
- يعمل على جميع الأجهزة
- تصميم Bootstrap 5
- واجهة مستخدم حديثة
- أيقونات Font Awesome

## 📁 هيكل المشروع

```
test/
├── index.html              # الصفحة الرئيسية
├── pages/
│   ├── about.html         # صفحة من نحن
│   ├── services.html      # صفحة الخدمات والمنتجات
│   └── contact.html       # صفحة التواصل
├── css/
│   ├── style.css         # الأنماط الرئيسية
│   └── themes.css        # أنماط الوضع الليلي/النهاري
├── js/
│   ├── main.js           # الوظائف الرئيسية
│   ├── cart.js           # إدارة السلة
│   ├── language.js       # تعدد اللغات
│   └── theme.js          # تبديل الوضع الليلي/النهاري
├── lang/
│   ├── ar.json           # النصوص العربية
│   ├── en.json           # النصوص الإنجليزية
│   └── tr.json           # النصوص التركية
├── data/
│   └── products.json     # بيانات المنتجات
└── images/
    ├── products/         # صور المنتجات
    └── icons/            # الأيقونات
```

## 🚀 كيفية التشغيل

### 1. تشغيل محلي
```bash
# باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx serve .

# أو باستخدام PHP
php -S localhost:8000
```

### 2. فتح الموقع
افتح المتصفح وانتقل إلى: `http://localhost:8000`

## 📋 الصفحات المتاحة

### 🏠 الصفحة الرئيسية (`index.html`)
- عرض المنتجات المميزة
- نظرة عامة على الخدمات
- قسم البطل (Hero Section)
- سلة التسوق

### ℹ️ من نحن (`pages/about.html`)
- معلومات عن الشركة
- الرؤية والرسالة والأهداف
- إحصائيات الشركة
- مميزات الخدمة

### 🛍️ الخدمات (`pages/services.html`)
- عرض جميع المنتجات
- تصفية حسب الفئة
- إضافة للسلة
- تفاصيل المنتجات

### 📞 تواصل معنا (`pages/contact.html`)
- نموذج التواصل
- معلومات الاتصال
- خريطة Google Maps
- روابط وسائل التواصل

## 🛠️ التخصيص

### إضافة منتجات جديدة
عدّل ملف `data/products.json`:

```json
{
  "id": 9,
  "category": "new_category",
  "name": {
    "ar": "اسم المنتج بالعربية",
    "en": "Product Name in English",
    "tr": "Türkçe Ürün Adı"
  },
  "description": {
    "ar": "وصف المنتج بالعربية",
    "en": "Product description in English",
    "tr": "Türkçe ürün açıklaması"
  },
  "price": "100",
  "currency": {
    "ar": "ريال",
    "en": "SAR",
    "tr": "SAR"
  },
  "unit": {
    "ar": "للقطعة",
    "en": "per piece",
    "tr": "adet başına"
  },
  "image": "fas fa-icon-name",
  "featured": true
}
```

### تعديل النصوص
عدّل ملفات اللغات في مجلد `lang/`:
- `ar.json` للعربية
- `en.json` للإنجليزية  
- `tr.json` للتركية

### تخصيص الألوان
عدّل متغيرات CSS في `css/style.css`:

```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    /* ... باقي المتغيرات */
}
```

### تغيير رقم واتساب
عدّل المتغير في `js/cart.js`:

```javascript
const WHATSAPP_NUMBER = '+966501234567'; // ضع رقمك هنا
```

## 🎨 الخطوط المستخدمة

- **Cairo**: للنصوص العربية
- **Poppins**: للنصوص الإنجليزية والتركية

## 📱 المكتبات المستخدمة

- **Bootstrap 5.3.0**: للتصميم المتجاوب
- **Font Awesome 6.4.0**: للأيقونات
- **Google Fonts**: للخطوط

## 🔧 الوظائف المتقدمة

### حفظ التفضيلات
- اللغة المفضلة
- الوضع الليلي/النهاري
- محتويات السلة

### تكامل واتساب
- إرسال تفاصيل الطلب تلقائياً
- تنسيق الرسالة بشكل احترافي
- تضمين معلومات العميل

### تجربة المستخدم
- انتقالات سلسة
- تحميل سريع
- واجهة بديهية
- دعم لوحة المفاتيح

## 📞 الدعم والتطوير

لأي استفسارات أو طلبات تطوير، يمكنك التواصل معنا عبر:
- البريد الإلكتروني: <EMAIL>
- واتساب: +966501234567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

**مطبعة طبعة** - خدمات الطباعة الاحترافية 🖨️
