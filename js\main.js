// Main JavaScript file for Tab3a Printing Website

// Global variables
let currentLanguage = 'ar';
let products = [];
let translations = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    try {
        // Load saved preferences
        loadUserPreferences();
        
        // Load translations and products
        await loadTranslations();
        await loadProducts();
        
        // Initialize components
        initializeNavigation();
        initializeLanguageSelector();
        initializeThemeToggle();
        
        // Load featured products on home page
        if (window.location.pathname.endsWith('index.html') || window.location.pathname === '/') {
            loadFeaturedProducts();
        }
        
        // Apply current language
        applyLanguage(currentLanguage);
        
    } catch (error) {
        console.error('Error initializing app:', error);
    }
}

// Load user preferences from localStorage
function loadUserPreferences() {
    const savedLanguage = localStorage.getItem('preferred_language');
    const savedTheme = localStorage.getItem('preferred_theme');
    
    if (savedLanguage) {
        currentLanguage = savedLanguage;
    }
    
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
    }
}

// Load translations from JSON files
async function loadTranslations() {
    try {
        const languages = ['ar', 'en', 'tr'];
        const basePath = window.location.pathname.includes('/pages/') ? '../' : '';
        const translationPromises = languages.map(async (lang) => {
            const response = await fetch(`${basePath}lang/${lang}.json`);
            const data = await response.json();
            return { lang, data };
        });

        const results = await Promise.all(translationPromises);
        results.forEach(({ lang, data }) => {
            translations[lang] = data;
        });

    } catch (error) {
        console.error('Error loading translations:', error);
    }
}

// Load products from JSON file
async function loadProducts() {
    try {
        const basePath = window.location.pathname.includes('/pages/') ? '../' : '';
        const response = await fetch(`${basePath}data/products.json`);
        const data = await response.json();
        products = data.products;
        window.productCategories = data.categories;
    } catch (error) {
        console.error('Error loading products:', error);
    }
}

// Initialize navigation
function initializeNavigation() {
    // Add active class to current page
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href.includes(currentPage)) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// Initialize language selector
function initializeLanguageSelector() {
    const currentLangElement = document.getElementById('currentLang');
    if (currentLangElement) {
        updateCurrentLanguageDisplay();
    }
}

// Initialize theme toggle
function initializeThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
        updateThemeIcon();
    }
}

// Load featured products on home page
function loadFeaturedProducts() {
    const featuredContainer = document.getElementById('featuredProducts');
    if (!featuredContainer || !products.length) return;
    
    const featuredProducts = products.filter(product => product.featured);
    
    featuredContainer.innerHTML = featuredProducts.map(product => `
        <div class="col-md-4 mb-4">
            <div class="product-card">
                <div class="product-image">
                    <i class="${product.image}"></i>
                </div>
                <div class="card-body">
                    <h5 class="product-title">${product.name[currentLanguage]}</h5>
                    <p class="product-description">${product.description[currentLanguage]}</p>
                    <div class="product-price">
                        ${product.price} ${product.currency[currentLanguage]} ${product.unit[currentLanguage]}
                    </div>
                    <button class="btn btn-primary w-100" onclick="addToCart(${product.id})">
                        <i class="fas fa-cart-plus me-2"></i>
                        <span data-lang="add_to_cart">${translations[currentLanguage]?.add_to_cart || 'أضف إلى السلة'}</span>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Apply language to the page
function applyLanguage(language) {
    currentLanguage = language;
    
    // Update HTML attributes
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    
    // Update all elements with data-lang attribute
    const elementsWithLang = document.querySelectorAll('[data-lang]');
    elementsWithLang.forEach(element => {
        const key = element.getAttribute('data-lang');
        if (translations[language] && translations[language][key]) {
            if (element.tagName === 'INPUT' && element.type === 'submit') {
                element.value = translations[language][key];
            } else {
                element.textContent = translations[language][key];
            }
        }
    });
    
    // Update page title
    const titleElement = document.querySelector('title[data-lang]');
    if (titleElement) {
        const titleKey = titleElement.getAttribute('data-lang');
        if (translations[language] && translations[language][titleKey]) {
            titleElement.textContent = translations[language][titleKey];
        }
    }
    
    // Update current language display
    updateCurrentLanguageDisplay();
    
    // Reload products if on products page
    if (typeof loadAllProducts === 'function') {
        loadAllProducts();
    }
    
    // Reload featured products if on home page
    if (document.getElementById('featuredProducts')) {
        loadFeaturedProducts();
    }
    
    // Save preference
    localStorage.setItem('preferred_language', language);
}

// Update current language display
function updateCurrentLanguageDisplay() {
    const currentLangElement = document.getElementById('currentLang');
    if (currentLangElement) {
        const languageNames = {
            'ar': 'العربية',
            'en': 'English',
            'tr': 'Türkçe'
        };
        currentLangElement.textContent = languageNames[currentLanguage];
    }
}

// Change language function (called from HTML)
function changeLanguage(language) {
    applyLanguage(language);
}

// Toggle theme function
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('preferred_theme', newTheme);
    
    updateThemeIcon();
}

// Update theme icon
function updateThemeIcon() {
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        if (currentTheme === 'dark') {
            themeIcon.className = 'fas fa-sun';
        } else {
            themeIcon.className = 'fas fa-moon';
        }
    }
}

// Add to cart function (will be implemented in cart.js)
function addToCart(productId) {
    if (typeof window.addProductToCart === 'function') {
        window.addProductToCart(productId);
    } else {
        console.log('Adding product to cart:', productId);
        // Fallback: show a simple alert
        alert(translations[currentLanguage]?.add_to_cart || 'تم إضافة المنتج إلى السلة');
    }
}

// Utility function to get current language
function getCurrentLanguage() {
    return currentLanguage;
}

// Utility function to get translation
function getTranslation(key) {
    return translations[currentLanguage]?.[key] || key;
}

// Utility function to get product by ID
function getProductById(id) {
    return products.find(product => product.id === id);
}

// Export functions for use in other files
window.getCurrentLanguage = getCurrentLanguage;
window.getTranslation = getTranslation;
window.getProductById = getProductById;
window.changeLanguage = changeLanguage;
window.addToCart = addToCart;

// Smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' && e.target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Add loading animation to buttons
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('btn') && !e.target.disabled && !e.target.closest('.navbar')) {
        const originalText = e.target.innerHTML;
        e.target.innerHTML = '<span class="loading"></span>';
        e.target.disabled = true;

        setTimeout(() => {
            e.target.innerHTML = originalText;
            e.target.disabled = false;
        }, 1000);
    }
});

// Performance optimization: Lazy load images
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', lazyLoadImages);

// Register Service Worker for PWA support
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful with scope: ', registration.scope);

                // Check for updates
                registration.addEventListener('updatefound', function() {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', function() {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New content is available, show update notification
                            showUpdateNotification();
                        }
                    });
                });
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed: ', err);
            });
    });
}

// Show update notification
function showUpdateNotification() {
    const notification = document.createElement('div');
    notification.className = 'alert alert-info position-fixed';
    notification.style.cssText = `
        top: 100px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;

    notification.innerHTML = `
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <i class="fas fa-sync-alt me-2"></i>
                <span>تحديث جديد متاح</span>
            </div>
            <button class="btn btn-sm btn-primary" onclick="window.location.reload()">
                تحديث
            </button>
        </div>
    `;

    document.body.appendChild(notification);
}

console.log('Main.js loaded successfully');
