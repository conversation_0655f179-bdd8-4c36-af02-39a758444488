@echo off
echo ========================================
echo    Tab3a Printing Website Launcher
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting Python HTTP Server...
    echo Server will be available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    python -m http.server 8000
    goto :end
)

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python not found. Checking for Node.js...
    npx --version >nul 2>&1
    if %errorlevel% == 0 (
        echo Starting Node.js HTTP Server...
        echo Server will be available at: http://localhost:8000
        echo Press Ctrl+C to stop the server
        echo.
        npx serve . -p 8000
        goto :end
    )
)

REM Check if PHP is available
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python and Node.js not found. Checking for PHP...
    echo Starting PHP Built-in Server...
    echo Server will be available at: http://localhost:8000
    echo Press Ctrl+C to stop the server
    echo.
    php -S localhost:8000
    goto :end
)

echo.
echo ERROR: No suitable server found!
echo Please install one of the following:
echo - Python 3.x
echo - Node.js with npx
echo - PHP 7.x or higher
echo.
echo Then run this script again.
pause

:end
echo.
echo Server stopped.
pause
