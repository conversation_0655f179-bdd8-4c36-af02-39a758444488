<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang="services_title">خدماتنا ومنتجاتنا - مطبعة طبعة</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/themes.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.html">
                <i class="fas fa-print me-2"></i>
                <span data-lang="brand_name">طبعة</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.html" data-lang="nav_home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html" data-lang="nav_about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="services.html" data-lang="nav_services">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html" data-lang="nav_contact">تواصل معنا</a>
                    </li>
                </ul>
                
                <div class="navbar-nav">
                    <!-- Cart -->
                    <div class="nav-item me-3">
                        <button class="btn btn-outline-primary position-relative" id="cartBtn">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cartCount">0</span>
                        </button>
                    </div>
                    
                    <!-- Language Selector -->
                    <div class="nav-item dropdown me-3">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe"></i>
                            <span id="currentLang">العربية</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('ar')">العربية</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('en')">English</a></li>
                            <li><a class="dropdown-item" href="#" onclick="changeLanguage('tr')">Türkçe</a></li>
                        </ul>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <div class="nav-item">
                        <button class="btn btn-outline-secondary" id="themeToggle">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="py-5 mt-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3" data-lang="services_title">خدماتنا ومنتجاتنا</h1>
                    <p class="lead" data-lang="services_subtitle">نقدم مجموعة شاملة من خدمات الطباعة عالية الجودة</p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-cogs display-1"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Category Filter -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex flex-wrap justify-content-center gap-2">
                        <button class="btn btn-outline-primary active" onclick="filterProducts('all')">
                            <i class="fas fa-th-large me-2"></i>جميع المنتجات
                        </button>
                        <button class="btn btn-outline-primary" onclick="filterProducts('cards')">
                            <i class="fas fa-id-card me-2"></i><span data-lang="category_cards">الكروت</span>
                        </button>
                        <button class="btn btn-outline-primary" onclick="filterProducts('banners')">
                            <i class="fas fa-sign me-2"></i><span data-lang="category_banners">اللوحات الإعلانية</span>
                        </button>
                        <button class="btn btn-outline-primary" onclick="filterProducts('books')">
                            <i class="fas fa-book me-2"></i><span data-lang="category_books">الدفاتر والفواتير</span>
                        </button>
                        <button class="btn btn-outline-primary" onclick="filterProducts('bags')">
                            <i class="fas fa-shopping-bag me-2"></i><span data-lang="category_bags">الأكياس</span>
                        </button>
                        <button class="btn btn-outline-primary" onclick="filterProducts('shirts')">
                            <i class="fas fa-tshirt me-2"></i><span data-lang="category_shirts">التيشيرتات</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="py-5">
        <div class="container">
            <div class="row" id="productsGrid">
                <!-- Products will be loaded here by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Cart Modal -->
    <div class="modal fade" id="cartModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" data-lang="cart_title">سلة التسوق</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="cartItems">
                        <!-- Cart items will be displayed here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">إغلاق</button>
                    <button type="button" class="btn btn-success" id="orderWhatsApp" data-lang="order_whatsapp">
                        <i class="fab fa-whatsapp me-2"></i>إتمام الطلب عبر واتساب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 data-lang="brand_name">طبعة</h5>
                    <p data-lang="footer_desc">مطبعة احترافية تقدم أفضل خدمات الطباعة</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-whatsapp"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-3">
            <div class="text-center">
                <p class="mb-0" data-lang="copyright">&copy; 2024 طبعة. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="../js/main.js"></script>
    <script src="../js/language.js"></script>
    <script src="../js/theme.js"></script>
    <script src="../js/cart.js"></script>
    
    <script>
        // Services page specific JavaScript
        let currentFilter = 'all';
        
        // Load all products when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                loadAllProducts();
            }, 500);
        });
        
        // Load all products
        function loadAllProducts() {
            const productsGrid = document.getElementById('productsGrid');
            if (!productsGrid || !window.products) return;
            
            const currentLang = getCurrentLanguage();
            
            productsGrid.innerHTML = window.products.map(product => `
                <div class="col-lg-4 col-md-6 mb-4 product-item" data-category="${product.category}">
                    <div class="product-card fade-in">
                        <div class="product-image">
                            <i class="${product.image}"></i>
                        </div>
                        <div class="card-body">
                            <h5 class="product-title">${product.name[currentLang]}</h5>
                            <p class="product-description">${product.description[currentLang]}</p>
                            <div class="product-price">
                                ${product.price} ${product.currency[currentLang]} ${product.unit[currentLang]}
                            </div>
                            <button class="btn btn-primary w-100" onclick="addToCart(${product.id})">
                                <i class="fas fa-cart-plus me-2"></i>
                                <span data-lang="add_to_cart">${getTranslation('add_to_cart')}</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        // Filter products by category
        function filterProducts(category) {
            currentFilter = category;
            
            // Update active button
            document.querySelectorAll('.btn-outline-primary').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Filter products
            const productItems = document.querySelectorAll('.product-item');
            productItems.forEach(item => {
                if (category === 'all' || item.getAttribute('data-category') === category) {
                    item.style.display = 'block';
                    item.classList.add('fade-in');
                } else {
                    item.style.display = 'none';
                }
            });
        }
        
        // Make loadAllProducts available globally
        window.loadAllProducts = loadAllProducts;
    </script>
</body>
</html>
