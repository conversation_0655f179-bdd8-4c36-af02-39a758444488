// Shopping Cart Management for Tab3a Printing Website

// Cart state
let cart = [];
const WHATSAPP_NUMBER = '+966501234567'; // Replace with actual WhatsApp number

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCart();
});

// Initialize cart functionality
function initializeCart() {
    loadCartFromStorage();
    updateCartDisplay();
    setupCartEventListeners();
}

// Setup event listeners for cart
function setupCartEventListeners() {
    // Cart button click
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.addEventListener('click', showCartModal);
    }
    
    // WhatsApp order button
    const orderWhatsAppBtn = document.getElementById('orderWhatsApp');
    if (orderWhatsAppBtn) {
        orderWhatsAppBtn.addEventListener('click', sendOrderToWhatsApp);
    }
}

// Load cart from localStorage
function loadCartFromStorage() {
    const savedCart = localStorage.getItem('shopping_cart');
    if (savedCart) {
        try {
            cart = JSON.parse(savedCart);
        } catch (error) {
            console.error('Error loading cart from storage:', error);
            cart = [];
        }
    }
}

// Save cart to localStorage
function saveCartToStorage() {
    try {
        localStorage.setItem('shopping_cart', JSON.stringify(cart));
    } catch (error) {
        console.error('Error saving cart to storage:', error);
    }
}

// Add product to cart
function addProductToCart(productId) {
    const product = getProductById(productId);
    if (!product) {
        console.error('Product not found:', productId);
        return;
    }
    
    // Check if product already exists in cart
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            quantity: 1,
            addedAt: new Date().toISOString()
        });
    }
    
    saveCartToStorage();
    updateCartDisplay();
    showAddToCartFeedback(product);
}

// Remove product from cart
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveCartToStorage();
    updateCartDisplay();
    updateCartModal();
}

// Update product quantity in cart
function updateCartQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        if (newQuantity <= 0) {
            removeFromCart(productId);
        } else {
            item.quantity = newQuantity;
            saveCartToStorage();
            updateCartDisplay();
            updateCartModal();
        }
    }
}

// Update cart display (badge)
function updateCartDisplay() {
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        cartCount.textContent = totalItems;
        cartCount.style.display = totalItems > 0 ? 'inline' : 'none';
    }
}

// Show cart modal
function showCartModal() {
    updateCartModal();
    const cartModal = new bootstrap.Modal(document.getElementById('cartModal'));
    cartModal.show();
}

// Update cart modal content
function updateCartModal() {
    const cartItems = document.getElementById('cartItems');
    if (!cartItems) return;
    
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-shopping-cart display-4 text-muted mb-3"></i>
                <h5 data-lang="cart_empty">${getTranslation('cart_empty')}</h5>
                <p class="text-muted" data-lang="cart_empty_desc">${getTranslation('cart_empty_desc')}</p>
                <button class="btn btn-primary" data-bs-dismiss="modal" data-lang="continue_shopping">
                    ${getTranslation('continue_shopping')}
                </button>
            </div>
        `;
        return;
    }
    
    let totalPrice = 0;
    const cartHTML = cart.map(item => {
        const product = getProductById(item.id);
        if (!product) return '';
        
        const itemTotal = parseFloat(product.price) * item.quantity;
        totalPrice += itemTotal;
        
        return `
            <div class="cart-item">
                <div class="row align-items-center">
                    <div class="col-2">
                        <div class="cart-item-image">
                            <i class="${product.image}"></i>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="mb-1">${product.name[getCurrentLanguage()]}</h6>
                        <small class="text-muted">${product.price} ${product.currency[getCurrentLanguage()]} ${product.unit[getCurrentLanguage()]}</small>
                    </div>
                    <div class="col-3">
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateCartQuantity(${item.id}, ${item.quantity - 1})">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" class="quantity-input" value="${item.quantity}" 
                                   onchange="updateCartQuantity(${item.id}, parseInt(this.value))" min="1">
                            <button class="quantity-btn" onclick="updateCartQuantity(${item.id}, ${item.quantity + 1})">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-2">
                        <strong>${itemTotal.toFixed(2)} ${product.currency[getCurrentLanguage()]}</strong>
                    </div>
                    <div class="col-1">
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})" 
                                title="${getTranslation('remove')}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    cartItems.innerHTML = `
        ${cartHTML}
        <div class="cart-total mt-3 pt-3 border-top">
            <div class="row">
                <div class="col-6">
                    <h5 data-lang="total">${getTranslation('total')}:</h5>
                </div>
                <div class="col-6 text-end">
                    <h5 class="text-primary">${totalPrice.toFixed(2)} ${getTranslation('currency') || 'ريال'}</h5>
                </div>
            </div>
        </div>
    `;
}

// Show add to cart feedback
function showAddToCartFeedback(product) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-success position-fixed';
    notification.style.cssText = `
        top: 100px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-check-circle me-2"></i>
            <div>
                <strong>${product.name[getCurrentLanguage()]}</strong><br>
                <small>${getTranslation('add_to_cart')} ✓</small>
            </div>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Send order to WhatsApp
function sendOrderToWhatsApp() {
    if (cart.length === 0) {
        alert(getTranslation('cart_empty'));
        return;
    }
    
    let message = getTranslation('order_whatsapp') + ':\n\n';
    let totalPrice = 0;
    
    cart.forEach(item => {
        const product = getProductById(item.id);
        if (product) {
            const itemTotal = parseFloat(product.price) * item.quantity;
            totalPrice += itemTotal;
            
            message += `• ${product.name[getCurrentLanguage()]}\n`;
            message += `  ${getTranslation('quantity')}: ${item.quantity}\n`;
            message += `  ${getTranslation('price')}: ${product.price} ${product.currency[getCurrentLanguage()]} ${product.unit[getCurrentLanguage()]}\n`;
            message += `  ${getTranslation('total')}: ${itemTotal.toFixed(2)} ${product.currency[getCurrentLanguage()]}\n\n`;
        }
    });
    
    message += `${getTranslation('total')}: ${totalPrice.toFixed(2)} ${getTranslation('currency') || 'ريال'}\n\n`;
    message += `${getTranslation('contact_info_title')}:\n`;
    message += `${getTranslation('contact_name')}: \n`;
    message += `${getTranslation('contact_phone')}: \n`;
    message += `${getTranslation('contact_address')}: `;
    
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${WHATSAPP_NUMBER}?text=${encodedMessage}`;
    
    window.open(whatsappUrl, '_blank');
}

// Clear cart
function clearCart() {
    cart = [];
    saveCartToStorage();
    updateCartDisplay();
    updateCartModal();
}

// Get cart summary
function getCartSummary() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = cart.reduce((sum, item) => {
        const product = getProductById(item.id);
        return sum + (product ? parseFloat(product.price) * item.quantity : 0);
    }, 0);
    
    return {
        items: cart,
        totalItems,
        totalPrice: totalPrice.toFixed(2)
    };
}

// Export functions for global use
window.addProductToCart = addProductToCart;
window.removeFromCart = removeFromCart;
window.updateCartQuantity = updateCartQuantity;
window.clearCart = clearCart;
window.getCartSummary = getCartSummary;

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

console.log('Cart.js loaded successfully');
