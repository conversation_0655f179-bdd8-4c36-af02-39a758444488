#!/bin/bash

echo "========================================"
echo "   Tab3a Printing Website Launcher"
echo "========================================"
echo

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to start server
start_server() {
    echo "Server will be available at: http://localhost:8000"
    echo "Press Ctrl+C to stop the server"
    echo
    
    # Open browser after a short delay
    (sleep 2 && open http://localhost:8000 2>/dev/null || xdg-open http://localhost:8000 2>/dev/null) &
    
    # Start the server
    $1
}

# Check for Python
if command_exists python3; then
    echo "Starting Python 3 HTTP Server..."
    start_server "python3 -m http.server 8000"
elif command_exists python; then
    echo "Starting Python HTTP Server..."
    start_server "python -m http.server 8000"
# Check for Node.js
elif command_exists node && command_exists npx; then
    echo "Python not found. Starting Node.js HTTP Server..."
    start_server "npx serve . -p 8000"
# Check for PHP
elif command_exists php; then
    echo "Python and Node.js not found. Starting PHP Built-in Server..."
    start_server "php -S localhost:8000"
else
    echo
    echo "ERROR: No suitable server found!"
    echo "Please install one of the following:"
    echo "- Python 3.x"
    echo "- Node.js with npx"
    echo "- PHP 7.x or higher"
    echo
    echo "Then run this script again."
    exit 1
fi

echo
echo "Server stopped."
