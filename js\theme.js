// Theme Management for Tab3a Printing Website

// Theme configuration
const THEMES = {
    light: {
        name: 'Light',
        icon: 'fas fa-sun',
        class: 'light'
    },
    dark: {
        name: 'Dark',
        icon: 'fas fa-moon',
        class: 'dark'
    }
};

// Initialize theme system
document.addEventListener('DOMContentLoaded', function() {
    initializeThemeSystem();
});

function initializeThemeSystem() {
    // Detect user's preferred theme
    detectUserTheme();
    
    // Set up theme toggle events
    setupThemeToggle();
    
    // Apply initial theme
    const currentTheme = getCurrentTheme();
    applyTheme(currentTheme);
    
    // Listen for system theme changes
    setupSystemThemeListener();
}

// Detect user's preferred theme
function detectUserTheme() {
    // Check if theme is already saved
    const savedTheme = localStorage.getItem('preferred_theme');
    if (savedTheme && THEMES[savedTheme]) {
        return savedTheme;
    }
    
    // Check system preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        localStorage.setItem('preferred_theme', 'dark');
        return 'dark';
    }
    
    // Default to light theme
    localStorage.setItem('preferred_theme', 'light');
    return 'light';
}

// Get current theme
function getCurrentTheme() {
    return localStorage.getItem('preferred_theme') || 'light';
}

// Setup theme toggle events
function setupThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleTheme();
        });
    }
    
    // Add keyboard support
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + Shift + T to toggle theme
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            toggleTheme();
        }
    });
}

// Toggle between themes
function toggleTheme() {
    const currentTheme = getCurrentTheme();
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    setTheme(newTheme);
}

// Set specific theme
function setTheme(theme) {
    if (!THEMES[theme]) {
        console.error('Invalid theme:', theme);
        return;
    }
    
    // Save preference
    localStorage.setItem('preferred_theme', theme);
    
    // Apply theme
    applyTheme(theme);
    
    // Update toggle button
    updateThemeToggleButton(theme);
    
    // Trigger custom event
    const event = new CustomEvent('themeChanged', {
        detail: { theme, previousTheme: getCurrentTheme() }
    });
    document.dispatchEvent(event);
}

// Apply theme to document
function applyTheme(theme) {
    if (!THEMES[theme]) {
        console.error('Invalid theme:', theme);
        return;
    }
    
    // Set data-theme attribute
    document.documentElement.setAttribute('data-theme', theme);
    
    // Update body class
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme}`);
    
    // Update meta theme-color for mobile browsers
    updateMetaThemeColor(theme);
    
    // Apply theme-specific adjustments
    applyThemeAdjustments(theme);
}

// Update theme toggle button
function updateThemeToggleButton(theme) {
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');
    
    if (themeToggle && themeIcon) {
        const themeConfig = THEMES[theme];
        
        // Update icon
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        // Update title
        const oppositeTheme = theme === 'dark' ? 'light' : 'dark';
        themeToggle.title = `Switch to ${THEMES[oppositeTheme].name} mode`;
        
        // Add animation
        themeIcon.style.transform = 'rotate(180deg)';
        setTimeout(() => {
            themeIcon.style.transform = 'rotate(0deg)';
        }, 300);
    }
}

// Update meta theme-color for mobile browsers
function updateMetaThemeColor(theme) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    
    if (!metaThemeColor) {
        metaThemeColor = document.createElement('meta');
        metaThemeColor.name = 'theme-color';
        document.head.appendChild(metaThemeColor);
    }
    
    const colors = {
        light: '#ffffff',
        dark: '#0f172a'
    };
    
    metaThemeColor.content = colors[theme] || colors.light;
}

// Apply theme-specific adjustments
function applyThemeAdjustments(theme) {
    // Update iframe styles (like Google Maps)
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach(iframe => {
        if (theme === 'dark') {
            iframe.style.filter = 'invert(0.9) hue-rotate(180deg)';
        } else {
            iframe.style.filter = 'none';
        }
    });
    
    // Update image filters if needed
    const images = document.querySelectorAll('img[data-theme-filter]');
    images.forEach(img => {
        if (theme === 'dark') {
            img.style.filter = img.getAttribute('data-theme-filter') || 'brightness(0.8)';
        } else {
            img.style.filter = 'none';
        }
    });
    
    // Update chart colors if any charts exist
    updateChartColors(theme);
}

// Update chart colors (placeholder for future chart implementations)
function updateChartColors(theme) {
    // This function can be extended when charts are added
    const charts = document.querySelectorAll('[data-chart]');
    charts.forEach(chart => {
        // Update chart theme
        const event = new CustomEvent('updateChartTheme', {
            detail: { theme }
        });
        chart.dispatchEvent(event);
    });
}

// Setup system theme change listener
function setupSystemThemeListener() {
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', function(e) {
            // Only auto-switch if user hasn't manually set a preference
            const hasManualPreference = localStorage.getItem('manual_theme_preference');
            
            if (!hasManualPreference) {
                const systemTheme = e.matches ? 'dark' : 'light';
                setTheme(systemTheme);
            }
        });
    }
}

// Mark that user has manually set theme preference
function markManualThemePreference() {
    localStorage.setItem('manual_theme_preference', 'true');
}

// Reset to system theme preference
function resetToSystemTheme() {
    localStorage.removeItem('manual_theme_preference');
    localStorage.removeItem('preferred_theme');
    
    const systemTheme = detectUserTheme();
    setTheme(systemTheme);
}

// Get theme preference info
function getThemeInfo() {
    const currentTheme = getCurrentTheme();
    const hasManualPreference = localStorage.getItem('manual_theme_preference') === 'true';
    const systemPrefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    return {
        current: currentTheme,
        hasManualPreference,
        systemPrefersDark,
        available: Object.keys(THEMES)
    };
}

// Enhanced toggle with manual preference tracking
function toggleThemeEnhanced() {
    markManualThemePreference();
    toggleTheme();
}

// Listen for theme change events
document.addEventListener('themeChanged', function(e) {
    console.log('Theme changed to:', e.detail.theme);
    
    // Update any theme-dependent components
    updateThemeDependentComponents(e.detail.theme);
    
    // Save analytics or user preference data
    trackThemeChange(e.detail.theme);
});

// Update components that depend on theme
function updateThemeDependentComponents(theme) {
    // Update syntax highlighting if any code blocks exist
    const codeBlocks = document.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
        block.className = block.className.replace(/theme-\w+/g, '');
        block.classList.add(`theme-${theme}`);
    });
    
    // Update any custom components
    const customComponents = document.querySelectorAll('[data-theme-component]');
    customComponents.forEach(component => {
        const event = new CustomEvent('themeUpdate', {
            detail: { theme }
        });
        component.dispatchEvent(event);
    });
}

// Track theme changes (placeholder for analytics)
function trackThemeChange(theme) {
    // This can be extended to send analytics data
    console.log('Theme analytics:', {
        theme,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
    });
}

// Add smooth transition for theme changes
function addThemeTransition() {
    const style = document.createElement('style');
    style.textContent = `
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
        }
        
        .theme-transition-disable * {
            transition: none !important;
        }
    `;
    document.head.appendChild(style);
}

// Temporarily disable transitions (useful for initial load)
function disableThemeTransitions() {
    document.body.classList.add('theme-transition-disable');
    setTimeout(() => {
        document.body.classList.remove('theme-transition-disable');
    }, 100);
}

// Initialize theme transitions
addThemeTransition();

// Export functions for global use
window.getCurrentTheme = getCurrentTheme;
window.setTheme = setTheme;
window.toggleTheme = toggleThemeEnhanced;
window.resetToSystemTheme = resetToSystemTheme;
window.getThemeInfo = getThemeInfo;
window.disableThemeTransitions = disableThemeTransitions;

console.log('Theme.js loaded successfully');
