<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار موقع مطبعة طبعة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .test-section { margin: 2rem 0; padding: 1.5rem; border: 1px solid #ddd; border-radius: 8px; }
        .test-result { margin: 0.5rem 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 اختبار شامل لموقع مطبعة طبعة</h1>
        
        <div class="test-section">
            <h3><i class="fas fa-server me-2"></i>اختبار الخادم والملفات</h3>
            <div id="serverTests"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-box me-2"></i>اختبار المنتجات</h3>
            <div id="productTests"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-language me-2"></i>اختبار اللغات</h3>
            <div id="languageTests"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-moon me-2"></i>اختبار الوضع الليلي والنهاري</h3>
            <div id="themeTests"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-shopping-cart me-2"></i>اختبار السلة</h3>
            <div id="cartTests"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-mobile-alt me-2"></i>اختبار التصميم المتجاوب</h3>
            <div id="responsiveTests"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-chart-line me-2"></i>ملخص النتائج</h3>
            <div id="summaryTests"></div>
        </div>
    </div>

    <script>
        let testResults = {
            passed: 0,
            failed: 0,
            warnings: 0
        };

        function addTestResult(containerId, testName, status, message) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            
            let icon = '';
            switch(status) {
                case 'success': icon = '✅'; testResults.passed++; break;
                case 'error': icon = '❌'; testResults.failed++; break;
                case 'warning': icon = '⚠️'; testResults.warnings++; break;
                case 'info': icon = 'ℹ️'; break;
            }
            
            div.innerHTML = `${icon} <strong>${testName}:</strong> ${message}`;
            container.appendChild(div);
        }

        async function testServer() {
            try {
                // Test main page
                const response = await fetch('/');
                if (response.ok) {
                    addTestResult('serverTests', 'الصفحة الرئيسية', 'success', 'تم تحميل الصفحة الرئيسية بنجاح');
                } else {
                    addTestResult('serverTests', 'الصفحة الرئيسية', 'error', 'فشل في تحميل الصفحة الرئيسية');
                }

                // Test CSS files
                const cssResponse = await fetch('/css/style.css');
                if (cssResponse.ok) {
                    addTestResult('serverTests', 'ملف CSS الرئيسي', 'success', 'تم تحميل ملف الأنماط بنجاح');
                } else {
                    addTestResult('serverTests', 'ملف CSS الرئيسي', 'error', 'فشل في تحميل ملف الأنماط');
                }

                // Test JS files
                const jsResponse = await fetch('/js/main.js');
                if (jsResponse.ok) {
                    addTestResult('serverTests', 'ملف JavaScript الرئيسي', 'success', 'تم تحميل ملف JavaScript بنجاح');
                } else {
                    addTestResult('serverTests', 'ملف JavaScript الرئيسي', 'error', 'فشل في تحميل ملف JavaScript');
                }

            } catch (error) {
                addTestResult('serverTests', 'اتصال الخادم', 'error', 'خطأ في الاتصال بالخادم: ' + error.message);
            }
        }

        async function testProducts() {
            try {
                const response = await fetch('/data/products.json');
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.products && Array.isArray(data.products)) {
                        addTestResult('productTests', 'بيانات المنتجات', 'success', `تم تحميل ${data.products.length} منتج بنجاح`);
                        
                        // Test product structure
                        const firstProduct = data.products[0];
                        if (firstProduct && firstProduct.id && firstProduct.name && firstProduct.description) {
                            addTestResult('productTests', 'هيكل المنتج', 'success', 'هيكل بيانات المنتج صحيح');
                        } else {
                            addTestResult('productTests', 'هيكل المنتج', 'error', 'هيكل بيانات المنتج غير صحيح');
                        }
                        
                        // Test featured products
                        const featuredProducts = data.products.filter(p => p.featured);
                        if (featuredProducts.length > 0) {
                            addTestResult('productTests', 'المنتجات المميزة', 'success', `يوجد ${featuredProducts.length} منتج مميز`);
                        } else {
                            addTestResult('productTests', 'المنتجات المميزة', 'warning', 'لا يوجد منتجات مميزة');
                        }
                        
                    } else {
                        addTestResult('productTests', 'بيانات المنتجات', 'error', 'بيانات المنتجات غير صحيحة');
                    }
                } else {
                    addTestResult('productTests', 'ملف المنتجات', 'error', 'فشل في تحميل ملف المنتجات');
                }
            } catch (error) {
                addTestResult('productTests', 'اختبار المنتجات', 'error', 'خطأ: ' + error.message);
            }
        }

        async function testLanguages() {
            const languages = ['ar', 'en', 'tr'];
            
            for (const lang of languages) {
                try {
                    const response = await fetch(`/lang/${lang}.json`);
                    if (response.ok) {
                        const data = await response.json();
                        const keysCount = Object.keys(data).length;
                        addTestResult('languageTests', `اللغة ${lang}`, 'success', `تم تحميل ${keysCount} مفتاح ترجمة`);
                    } else {
                        addTestResult('languageTests', `اللغة ${lang}`, 'error', 'فشل في تحميل ملف اللغة');
                    }
                } catch (error) {
                    addTestResult('languageTests', `اللغة ${lang}`, 'error', 'خطأ: ' + error.message);
                }
            }
        }

        function testThemes() {
            // Test if theme CSS is loaded
            const themeCSS = document.querySelector('link[href*="themes.css"]');
            if (themeCSS) {
                addTestResult('themeTests', 'ملف CSS للوضع الليلي', 'success', 'تم تحميل ملف أنماط الوضع الليلي');
            } else {
                addTestResult('themeTests', 'ملف CSS للوضع الليلي', 'error', 'لم يتم العثور على ملف أنماط الوضع الليلي');
            }

            // Test localStorage theme support
            try {
                localStorage.setItem('test_theme', 'dark');
                const saved = localStorage.getItem('test_theme');
                localStorage.removeItem('test_theme');
                
                if (saved === 'dark') {
                    addTestResult('themeTests', 'حفظ تفضيل الوضع', 'success', 'يمكن حفظ تفضيل الوضع في المتصفح');
                } else {
                    addTestResult('themeTests', 'حفظ تفضيل الوضع', 'error', 'لا يمكن حفظ تفضيل الوضع');
                }
            } catch (error) {
                addTestResult('themeTests', 'حفظ تفضيل الوضع', 'error', 'خطأ في localStorage');
            }

            // Test CSS variables
            const rootStyles = getComputedStyle(document.documentElement);
            const primaryColor = rootStyles.getPropertyValue('--primary-color');
            if (primaryColor) {
                addTestResult('themeTests', 'متغيرات CSS', 'success', 'متغيرات CSS للألوان تعمل بشكل صحيح');
            } else {
                addTestResult('themeTests', 'متغيرات CSS', 'warning', 'لم يتم العثور على متغيرات CSS');
            }
        }

        function testCart() {
            // Test localStorage for cart
            try {
                const testCart = [{ id: 1, quantity: 2 }];
                localStorage.setItem('test_cart', JSON.stringify(testCart));
                const saved = JSON.parse(localStorage.getItem('test_cart'));
                localStorage.removeItem('test_cart');
                
                if (saved && saved.length === 1 && saved[0].id === 1) {
                    addTestResult('cartTests', 'حفظ السلة', 'success', 'يمكن حفظ بيانات السلة في المتصفح');
                } else {
                    addTestResult('cartTests', 'حفظ السلة', 'error', 'لا يمكن حفظ بيانات السلة');
                }
            } catch (error) {
                addTestResult('cartTests', 'حفظ السلة', 'error', 'خطأ في حفظ السلة');
            }

            // Test WhatsApp URL generation
            const testMessage = 'طلب جديد: كروت × 2';
            const encodedMessage = encodeURIComponent(testMessage);
            const whatsappUrl = `https://wa.me/966501234567?text=${encodedMessage}`;
            
            if (whatsappUrl.includes('wa.me') && whatsappUrl.includes('966501234567')) {
                addTestResult('cartTests', 'رابط واتساب', 'success', 'يمكن إنشاء رابط واتساب للطلبات');
            } else {
                addTestResult('cartTests', 'رابط واتساب', 'error', 'خطأ في إنشاء رابط واتساب');
            }
        }

        function testResponsive() {
            const screenWidth = window.innerWidth;
            
            if (screenWidth >= 1200) {
                addTestResult('responsiveTests', 'حجم الشاشة', 'info', `شاشة كبيرة (${screenWidth}px) - مناسب للكمبيوتر`);
            } else if (screenWidth >= 768) {
                addTestResult('responsiveTests', 'حجم الشاشة', 'info', `شاشة متوسطة (${screenWidth}px) - مناسب للتابلت`);
            } else {
                addTestResult('responsiveTests', 'حجم الشاشة', 'info', `شاشة صغيرة (${screenWidth}px) - مناسب للموبايل`);
            }

            // Test viewport meta tag
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport && viewport.content.includes('width=device-width')) {
                addTestResult('responsiveTests', 'Viewport Meta Tag', 'success', 'تم تعيين viewport بشكل صحيح');
            } else {
                addTestResult('responsiveTests', 'Viewport Meta Tag', 'error', 'viewport غير مُعرف بشكل صحيح');
            }

            // Test Bootstrap
            if (typeof bootstrap !== 'undefined') {
                addTestResult('responsiveTests', 'Bootstrap', 'success', 'تم تحميل Bootstrap بنجاح');
            } else {
                addTestResult('responsiveTests', 'Bootstrap', 'warning', 'Bootstrap غير محمل أو غير متاح');
            }
        }

        function showSummary() {
            const total = testResults.passed + testResults.failed + testResults.warnings;
            const successRate = ((testResults.passed / total) * 100).toFixed(1);
            
            const summaryContainer = document.getElementById('summaryTests');
            summaryContainer.innerHTML = `
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body">
                                <h5 class="card-title text-success">${testResults.passed}</h5>
                                <p class="card-text">اختبارات نجحت</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body">
                                <h5 class="card-title text-danger">${testResults.failed}</h5>
                                <p class="card-text">اختبارات فشلت</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body">
                                <h5 class="card-title text-warning">${testResults.warnings}</h5>
                                <p class="card-text">تحذيرات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body">
                                <h5 class="card-title text-info">${successRate}%</h5>
                                <p class="card-text">معدل النجاح</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <h5>تقييم عام: ${successRate >= 90 ? '🌟 ممتاز' : successRate >= 75 ? '👍 جيد' : successRate >= 50 ? '⚠️ يحتاج تحسين' : '❌ يحتاج إصلاح'}</h5>
                </div>
            `;
        }

        // Run all tests
        async function runAllTests() {
            console.log('🧪 بدء الاختبارات الشاملة...');
            
            await testServer();
            await testProducts();
            await testLanguages();
            testThemes();
            testCart();
            testResponsive();
            showSummary();
            
            console.log('✅ انتهت جميع الاختبارات');
        }

        // Start tests when page loads
        window.addEventListener('load', runAllTests);
    </script>
</body>
</html>
