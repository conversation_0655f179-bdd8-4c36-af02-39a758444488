/* Root Variables */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #ffffff;
    transition: var(--transition);
}

/* RTL/LTR Support */
[dir="rtl"] {
    font-family: 'Cairo', sans-serif;
}

[dir="ltr"] {
    font-family: 'Poppins', sans-serif;
}

/* Navigation Styles */
.navbar {
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95) !important;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 600;
    margin: 0 0.25rem;
    transition: var(--transition);
    border-radius: var(--border-radius-lg);
    padding: 0.75rem 1.5rem !important;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition);
    z-index: -1;
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    left: 0;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: white !important;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding-top: 100px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    font-weight: 400;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-image {
    text-align: center;
    position: relative;
    z-index: 2;
}

.hero-image i {
    font-size: 8rem;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        will-change: transform;
    }
    50% {
        transform: translateY(-20px) rotate(2deg);
        will-change: transform;
    }
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    padding: 0.875rem 2rem;
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    border-color: transparent;
    color: white;
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-xl);
    filter: brightness(1.1);
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    position: relative;
}

.btn-outline-primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition);
    z-index: -1;
}

.btn-outline-primary:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-lg);
}

.btn-outline-primary:hover::after {
    width: 100%;
}

.btn-lg {
    padding: 1rem 2.5rem;
    font-size: 1.125rem;
    border-radius: var(--border-radius-xl);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-sm);
}

/* Service Cards */
.service-card {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--box-shadow-xl);
    border-color: rgba(99, 102, 241, 0.2);
}

.service-card .display-4 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: var(--transition);
}

.service-card:hover .display-4 {
    transform: scale(1.1) rotate(5deg);
}

/* Product Cards */
.product-card {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.product-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.product-card:hover::after {
    opacity: 0.05;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: var(--box-shadow-xl);
    border-color: rgba(99, 102, 241, 0.3);
}

/* Feature Cards */
.feature-card {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.feature-icon i {
    transition: var(--transition);
}

.feature-card:hover .feature-icon i {
    transform: scale(1.1) rotate(5deg);
}

/* Hero Stats */
.hero-stats .stat-item {
    position: relative;
}

.hero-stats .stat-number {
    font-weight: 800;
    background: linear-gradient(45deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Call to Action Section */
.bg-primary {
    background: var(--gradient-primary) !important;
}

/* Enhanced Product Image */
.product-image {
    height: 220px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.product-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.product-image i {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
}

/* Filter Buttons */
.filter-buttons {
    margin-bottom: 2rem;
}

.filter-btn {
    border-radius: var(--border-radius-xl);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition);
    z-index: -1;
}

.filter-btn.active::before,
.filter-btn:hover::before {
    left: 0;
}

.filter-btn.active,
.filter-btn:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* Header Icon Animation */
.header-icon i {
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Badge Styles */
.badge {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.badge:hover {
    transform: scale(1.05);
}

/* Contact Form Card */
.contact-form-card {
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.contact-form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.form-icon i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Enhanced Form Controls */
.form-floating > .form-control,
.form-floating > .form-select {
    border-radius: var(--border-radius-lg);
    border: 2px solid #e2e8f0;
    transition: var(--transition);
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    background-color: rgba(255, 255, 255, 0.95);
}

.form-floating > label {
    color: var(--gray-600);
    font-weight: 500;
}

/* Contact Icon Animation */
.contact-icon i {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Contact Methods */
.contact-methods .btn {
    border-radius: var(--border-radius-xl);
    font-weight: 600;
    transition: var(--transition);
}

.contact-methods .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* Map Container */
.map-container {
    height: 400px;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--box-shadow-lg);
    transition: var(--transition);
}

.map-container:hover {
    transform: scale(1.02);
    box-shadow: var(--box-shadow-xl);
}

/* About Page Styles */
.about-highlights {
    margin-top: 1.5rem;
}

.highlight-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    font-weight: 600;
}

.highlight-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.about-icon i {
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
}

/* About Section Cards */
.about-section {
    position: relative;
    padding: 2rem;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    height: 100%;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
}

.about-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.about-section .icon-box {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    transition: var(--transition);
}

.about-section:hover .icon-box {
    transform: scale(1.1) rotate(5deg);
}

/* Statistics Section */
.stat-item {
    text-align: center;
    padding: 1rem;
    transition: var(--transition);
}

.stat-item:hover {
    transform: scale(1.05);
}

.stat-item .display-4 {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.product-card .card-body {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.product-description {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 1rem;
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 0;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #f0f9ff, #e0f2fe);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.quantity-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid #d1d5db;
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

/* Form Styles */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #d1d5db;
    padding: 0.75rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

/* Modal Styles */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid #e2e8f0;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e2e8f0;
    padding: 1.5rem;
}

/* Footer Styles */
footer {
    background-color: var(--dark-color) !important;
}

.social-links a {
    font-size: 1.5rem;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding-top: 100px;
        padding-bottom: 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 2rem !important;
    }

    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }

    .service-card,
    .product-card,
    .feature-card,
    .about-section {
        margin-bottom: 1.5rem;
    }

    .filter-buttons {
        gap: 0.5rem !important;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .contact-form-card {
        padding: 2rem 1.5rem;
    }

    .about-highlights {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem !important;
    }

    .highlight-item {
        text-align: center;
        min-width: 200px;
    }

    .contact-methods {
        justify-content: center;
        gap: 0.5rem !important;
    }

    .contact-methods .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .display-5 {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .service-card,
    .product-card,
    .feature-card {
        padding: 1.5rem;
    }

    .about-section {
        padding: 1.5rem;
    }

    .contact-form-card {
        padding: 1.5rem 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.border-primary { border-color: var(--primary-color) !important; }

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Print Styles */
@media print {
    .navbar,
    .modal,
    footer {
        display: none !important;
    }
    
    .hero-section {
        padding-top: 0;
        min-height: auto;
    }
}
